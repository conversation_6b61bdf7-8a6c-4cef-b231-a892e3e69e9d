import { Command } from 'commander'
import path from 'path'
import fs from 'fs'
import crypto from 'crypto'

const program = new Command()

program
  .option('-t, --target <string>', '输入音频文件路径')
  .option('-o, --output <string>', '输出文件路径（可选，默认为同名.md文件）')

program.parse(process.argv)

const options = {
    file: String(program.opts().target ?? ''),
    output: String(program.opts().output ?? '')
} as const

// 美团语音识别配置
const ASR_CONFIG = {
    baseUrl: 'https://aispeech.sankuai.com',
    token: '12345567896dfsfdsfdfds',
    endpoint: '/asr/v1/long_stream_recognize'
}

interface AsrParams {
    audio_format: string
    sample_rate: number
    channel_num: number
    index: number
    data_type: string
    enable_vad: number
    enable_include_vad_result: number
    enable_speaker_recognize: number
    stream_type: number
    text_normalization: number
}

interface AsrResponse {
    errcode: number
    errmsg: string
    data: {
        sessionId: string
        subSessionId: string
        resIndex: number
        text: string
        status: number
        speechTime: number
        startTime: number
        endTime: number
        sentences: Array<{
            content: string
            confidence: number
            words: Array<{
                content: string
                confidence: number
                startTime: number
                endTime: number
            }>
        }>
        vadInfos: Array<{
            start: number
            end: number
        }>
        bizData: string
    }
}

function generateSessionId(): string {
    return crypto.randomUUID()
}

function encodeAsrParams(params: AsrParams): string {
    return Buffer.from(JSON.stringify(params)).toString('base64')
}

async function callAsrApi(audioBuffer: Buffer, sessionId: string): Promise<AsrResponse> {
    const asrParams: AsrParams = {
        audio_format: 'wav',
        sample_rate: 16000, // 假设是16kHz，可能需要根据实际音频调整
        channel_num: 1,
        index: -1, // 单次完整音频，使用负数结尾
        data_type: 'binary',
        enable_vad: 1,
        enable_include_vad_result: 1,
        enable_speaker_recognize: 1, // 启用说话人识别
        stream_type: 0,
        text_normalization: 15 // 启用所有文本标准化 (1|2|4|8)
    }

    const headers = {
        'Content-Type': 'application/octet-stream',
        'Token': ASR_CONFIG.token,
        'SessionID': sessionId,
        'Set': 'rtasr',
        'Asr-Params': encodeAsrParams(asrParams)
    }

    try {
        const response = await fetch(`${ASR_CONFIG.baseUrl}${ASR_CONFIG.endpoint}`, {
            method: 'POST',
            headers,
            body: audioBuffer
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json() as AsrResponse
        return result
    } catch (error) {
        console.error('ASR API调用失败:', error)
        throw error
    }
}

function formatRecognitionResult(response: AsrResponse): string {
    if (response.errcode !== 0) {
        throw new Error(`识别失败: ${response.errmsg}`)
    }

    const { data } = response
    let output = `# 语音识别结果\n\n`
    output += `**会话ID**: ${data.sessionId}\n`
    output += `**识别时长**: ${Math.round(data.speechTime / 1000)}秒\n\n`

    // 如果有VAD信息，按时间段组织内容
    if (data.vadInfos && data.vadInfos.length > 0) {
        output += `## 按时间段识别结果\n\n`
        data.vadInfos.forEach((vad, index) => {
            const startTime = Math.round(vad.start / 1000)
            const endTime = Math.round(vad.end / 1000)
            output += `### 片段 ${index + 1} (${startTime}s - ${endTime}s)\n\n`

            // 这里需要根据实际返回的数据结构来匹配对应时间段的文本
            // 由于文档中说话人识别暂不支持，我们先按基本格式输出
            if (data.text) {
                output += `${data.text}\n\n`
            }
        })
    } else {
        // 如果没有VAD信息，直接输出识别文本
        output += `## 识别内容\n\n`
        if (data.text) {
            output += `${data.text}\n\n`
        }
    }

    // 如果有详细的句子信息
    if (data.sentences && data.sentences.length > 0) {
        output += `## 详细识别结果\n\n`
        data.sentences.forEach((sentence, index) => {
            output += `**句子 ${index + 1}** (置信度: ${sentence.confidence.toFixed(2)})\n`
            output += `${sentence.content}\n\n`

            if (sentence.words && sentence.words.length > 0) {
                output += `**词语详情**:\n`
                sentence.words.forEach(word => {
                    const startTime = Math.round(word.startTime / 1000)
                    const endTime = Math.round(word.endTime / 1000)
                    output += `- ${word.content} (${startTime}s-${endTime}s, 置信度: ${word.confidence.toFixed(2)})\n`
                })
                output += `\n`
            }
        })
    }

    return output
}

async function processAudioFile() {
    if (!options.file) {
        console.log('❌ 请提供要识别的音频文件，例如：`node audio-qa.ts -t audio.wav`')
        throw new Error('没有提供音频文件')
    }

    const audioPath = path.resolve(process.cwd(), options.file)

    if (!fs.existsSync(audioPath)) {
        throw new Error(`音频文件不存在: ${audioPath}`)
    }

    console.log(`🎵 开始处理音频文件: ${audioPath}`)

    // 读取音频文件
    const audioBuffer = fs.readFileSync(audioPath)
    console.log(`📁 音频文件大小: ${Math.round(audioBuffer.length / 1024 / 1024 * 100) / 100}MB`)

    // 生成会话ID
    const sessionId = generateSessionId()
    console.log(`🔑 会话ID: ${sessionId}`)

    // 调用语音识别API
    console.log('🚀 开始语音识别...')
    const response = await callAsrApi(audioBuffer, sessionId)

    // 格式化结果
    const formattedResult = formatRecognitionResult(response)

    // 确定输出文件路径
    const outputPath = options.output ||
        path.join(path.dirname(audioPath), path.basename(audioPath, path.extname(audioPath)) + '.md')

    // 保存结果
    fs.writeFileSync(outputPath, formattedResult, 'utf-8')

    console.log(`✅ 识别完成！结果已保存到: ${outputPath}`)
    console.log(`📝 识别文本长度: ${response.data.text?.length || 0} 字符`)

    return {
        audioPath,
        outputPath,
        response,
        formattedResult
    }
}

async function main() {
    try {
        await processAudioFile()
    } catch (error) {
        console.error('❌ 处理失败:', error.message)
        process.exit(1)
    }
}

main()